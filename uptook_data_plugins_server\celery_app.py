from __future__ import absolute_import, unicode_literals
import os
from celery import Celery
import logging.config
from django.conf import settings


# 设置Django默认设置模块
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'uptook_data_plugins_server.settings')




# 初始化celery
app = Celery('uptook_data_plugins_server')

# 使用Django的设置文件配置Celery
app.config_from_object('django.conf:settings', namespace='CELERY')

# 自动从所有已注册的Django app中加载任务
app.autodiscover_tasks()

from celery.signals import worker_process_init
@worker_process_init.connect
def configure_logging(**kwargs):
    from public_utils_configs.util.logger import ConcurrentTimedRotatingFileHandler
    ConcurrentTimedRotatingFileHandler.init_logger(level='INFO')
    logger = logging.getLogger(__name__)
    logger.info("Celery Logger initialized.")
version: '3.8'  # docker-compose.yml文件格式的版本号
services:
  plugins_server:
    container_name: plugins_server  # 容器名(指定完整的容器名，会忽略上面的容器名)
    build:
      context: .  # 指定当前目录为Dockerfile的目录
      dockerfile: ./Dockerfile  # 指定Dockerfile的路径和文件名(适用于不一样Dockerfile文件名的情况)
    image: uptook_data_plugins_server:0.1  # 镜像名:标签
    command: python3 manage.py runserver 0.0.0.0:9000  # 容器的启动命令
    working_dir: /app # 容器的工作目录
    ports: # 端口映射
      - "9000:9000"
    volumes: # 数据卷
      - ./logs:/app/logs
    logging: # 日志
      options:
        max-size: "100m"
        max-file: "5"
    deploy:
      resources:
        limits:
          memory: 6g  # 内存限制
    restart: on-failure  # 非0 状态码结束时重启
  
  celery:
    build:
      context: .
      dockerfile: ./Dockerfile
    container_name: celery_worker
    command: celery -A uptook_data_plugins_server worker --loglevel=info
    volumes:
      - .:/app
    depends_on:
      - plugins_server
    environment:
      - DJANGO_SETTINGS_MODULE=uptook_data_plugins_server.settings


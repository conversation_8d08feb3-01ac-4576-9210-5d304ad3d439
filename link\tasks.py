from celery import shared_task
from .funcs import funcs_obj
from .route_models import LinkAddReq, LinkResultResp
from .models import LinkTask, LinkResultMap
from django.db import transaction
from settings import logging
from .cos_utils import get_html_from_cos
import traceback

@shared_task()
def process_linkedin_data(link_url: str, cos_path: str, is_zip: int, task_md5: str):
    """
    异步处理LinkedIn数据的Celery任务
    """
    try:
        with transaction.atomic(using='db_link'):
            if LinkTask.objects.using('db_link').filter(task_md5=task_md5, status=2).exists():
                logging.info(f"Task {task_md5} already completed. Skipping.")
                return True

        html_str = get_html_from_cos(cos_path)

        # 处理数据
        result = funcs_obj.deal_req(
            link_url=link_url, html_str=html_str, is_zip=is_zip)
        with transaction.atomic(using='db_link'):
            LinkResultMap.objects.using('db_link').create(
                task_md5=task_md5, human_ids=result.human_ids, company_ids=result.company_ids
            )
        
        # 更新任务状态和结果
        
            LinkTask.objects.using('db_link').filter(task_md5=task_md5).update(status=2)
            logging.info(f"{task_md5} process sucess")
        return True
    except Exception as e:
        logging.error(f"Celery task error: {str(e)}")
        logging.error(f'{traceback.format_exc()}')
        # 更新任务状态为失败
        with transaction.atomic(using='db_link'):
            LinkTask.objects.using('db_link').filter(task_md5=task_md5).update(status=3)
        return False